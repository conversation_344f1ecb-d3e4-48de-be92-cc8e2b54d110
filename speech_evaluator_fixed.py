#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
科大讯飞语音评测API封装 - 修复版本
"""

import json
import time
import hashlib
import base64
import hmac
import urllib.parse
import ssl
import websocket
import threading
from datetime import datetime, timezone
import wave
import os

class XunfeiSpeechEvaluator:
    def __init__(self, app_id, api_secret, api_key):
        self.app_id = app_id
        self.api_secret = api_secret
        self.api_key = api_key
        self.base_url = "wss://ise-api.xfyun.cn/v2/open-ise"
        self.result = None
        self.error = None
        self.finished = False
        
    def create_url(self):
        """创建WebSocket连接URL - 修复版本"""
        # 生成RFC1123格式的UTC时间戳
        now = datetime.now(timezone.utc)
        date = now.strftime('%a, %d %b %Y %H:%M:%S GMT')
        
        # 拼接字符串
        signature_origin = "host: ise-api.xfyun.cn\n"
        signature_origin += "date: " + date + "\n"
        signature_origin += "GET /v2/open-ise HTTP/1.1"
        
        # 进行hmac-sha256进行加密
        signature_sha = hmac.new(self.api_secret.encode('utf-8'), 
                                signature_origin.encode('utf-8'),
                                digestmod=hashlib.sha256).digest()
        signature_sha = base64.b64encode(signature_sha).decode(encoding='utf-8')
        
        authorization_origin = f'api_key="{self.api_key}", algorithm="hmac-sha256", headers="host date request-line", signature="{signature_sha}"'
        authorization = base64.b64encode(authorization_origin.encode('utf-8')).decode(encoding='utf-8')
        
        # 将请求的鉴权参数组合为字典
        v = {
            "authorization": authorization,
            "date": date,
            "host": "ise-api.xfyun.cn"
        }
        
        # 拼接鉴权参数，生成url
        url = self.base_url + '?' + urllib.parse.urlencode(v)
        print(f"生成的URL: {url[:100]}...")  # 调试信息
        return url

    def evaluate(self, text, category="read_sentence", audio_file=None):
        """
        执行语音评测
        
        Args:
            text: 评测文本
            category: 评测类型 (read_syllable, read_word, read_sentence, read_chapter)
            audio_file: 音频文件路径
            
        Returns:
            tuple: (result, error)
        """
        self.result = None
        self.error = None
        self.finished = False
        
        # 构建评测参数
        params = {
            "app_id": self.app_id,
            "category": category,
            "language": "zh_cn",
            "text_encoding": "utf-8",
            "result_level": "complete",
            "plev": 0,  # 全维度评测
            "text": text
        }
        
        print(f"评测参数: {params}")  # 调试信息
        
        def on_message(ws, message):
            try:
                print(f"收到消息: {message[:200]}...")  # 调试信息
                data = json.loads(message)
                if data.get('code') == 0:
                    self.result = data.get('data')
                    print("评测成功!")
                else:
                    self.error = f"评测失败: {data.get('message', '未知错误')} (code: {data.get('code')})"
                    print(self.error)
            except Exception as e:
                self.error = f"解析结果失败: {str(e)}"
                print(self.error)
            finally:
                self.finished = True
                ws.close()

        def on_error(ws, error):
            self.error = f"WebSocket错误: {str(error)}"
            print(self.error)
            self.finished = True

        def on_close(ws, close_status_code, close_msg):
            print(f"连接关闭: {close_status_code}, {close_msg}")
            self.finished = True

        def on_open(ws):
            def run():
                try:
                    print("连接已建立，发送参数...")
                    # 发送参数
                    ws.send(json.dumps(params))
                    
                    # 发送音频数据
                    if audio_file and os.path.exists(audio_file):
                        print(f"发送音频文件: {audio_file}")
                        audio_data = self._load_audio_file(audio_file)
                        
                        # 分块发送音频
                        chunk_size = 1280
                        total_chunks = len(audio_data) // chunk_size + 1
                        print(f"音频数据大小: {len(audio_data)} 字节, 分 {total_chunks} 块发送")
                        
                        for i in range(0, len(audio_data), chunk_size):
                            chunk = audio_data[i:i + chunk_size]
                            ws.send(chunk, websocket.ABNF.OPCODE_BINARY)
                            time.sleep(0.01)
                        
                        # 发送结束标志
                        ws.send(b'', websocket.ABNF.OPCODE_BINARY)
                        print("音频发送完成")
                    else:
                        self.error = "音频文件不存在或路径无效"
                        ws.close()
                        return
                    
                except Exception as e:
                    self.error = f"发送数据失败: {str(e)}"
                    print(self.error)
                    ws.close()
            
            thread = threading.Thread(target=run)
            thread.start()

        try:
            # 创建WebSocket连接
            url = self.create_url()
            print(f"连接到: {url[:50]}...")
            
            ws = websocket.WebSocketApp(url,
                                      on_message=on_message,
                                      on_error=on_error,
                                      on_close=on_close,
                                      on_open=on_open)
            
            # 设置SSL选项
            ws.run_forever(sslopt={"cert_reqs": ssl.CERT_NONE})
            
            # 等待结果
            timeout = 30  # 30秒超时
            start_time = time.time()
            while not self.finished and (time.time() - start_time) < timeout:
                time.sleep(0.1)
            
            if not self.finished:
                self.error = "评测超时"
            
        except Exception as e:
            self.error = f"连接失败: {str(e)}"
            print(f"连接异常: {e}")
        
        return self.result, self.error

    def _load_audio_file(self, audio_file):
        """加载音频文件"""
        try:
            if audio_file.endswith('.wav'):
                with wave.open(audio_file, 'rb') as wav_file:
                    # 检查音频格式
                    channels = wav_file.getnchannels()
                    sample_width = wav_file.getsampwidth()
                    framerate = wav_file.getframerate()
                    
                    print(f"音频格式: {channels}声道, {sample_width*8}bit, {framerate}Hz")
                    
                    # 读取音频数据
                    frames = wav_file.readframes(-1)
                    return frames
            else:
                # 对于其他格式，需要转换为WAV
                return self._convert_to_wav(audio_file)
                
        except Exception as e:
            raise Exception(f"加载音频文件失败: {str(e)}")

    def _convert_to_wav(self, audio_file):
        """转换音频文件为WAV格式"""
        try:
            from audio_converter import AudioConverter
            converter = AudioConverter()
            
            # 转换为标准格式
            converted_file = converter.convert_to_standard_format(audio_file)
            
            if converted_file:
                with wave.open(converted_file, 'rb') as wav_file:
                    frames = wav_file.readframes(-1)
                
                # 如果是临时文件，删除它
                if converted_file != audio_file:
                    try:
                        os.unlink(converted_file)
                    except:
                        pass
                
                return frames
            else:
                raise Exception("音频格式转换失败")
                
        except ImportError:
            raise Exception("需要安装audio_converter模块")
        except Exception as e:
            raise Exception(f"音频转换失败: {str(e)}")

    def test_connection(self):
        """测试API连接"""
        try:
            print("测试API连接...")
            url = self.create_url()
            print(f"测试URL生成成功: {len(url)} 字符")
            return True, "URL生成成功"
        except Exception as e:
            return False, str(e)

# 测试函数
def test_api():
    """测试API连接"""
    # 使用您的API密钥
    evaluator = XunfeiSpeechEvaluator(
        app_id="c94ba137",
        api_secret="OTg5ZGU2MjcxNDVkNjRkOTNiMmYyN2U2",
        api_key="98cf67ac3d06e558bd1ab6db7438acd6"
    )
    
    # 测试连接
    success, message = evaluator.test_connection()
    print(f"连接测试: {message}")
    
    return evaluator

if __name__ == "__main__":
    test_api()
