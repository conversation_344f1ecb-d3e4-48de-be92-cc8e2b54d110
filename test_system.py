#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
普通话评测系统测试脚本
"""

import os
import json
import sys
from pathlib import Path

def test_dependencies():
    """测试依赖包"""
    print("=== 测试依赖包 ===")
    
    required_packages = [
        'numpy',
        'websocket',
        'pydub',
        'scipy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✓ {package} - 已安装")
        except ImportError:
            print(f"✗ {package} - 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    else:
        print("\n所有依赖包已安装")
        return True

def test_config():
    """测试配置文件"""
    print("\n=== 测试配置文件 ===")
    
    if not os.path.exists("config.json"):
        print("✗ config.json 不存在")
        return False
    
    try:
        with open("config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        required_keys = ['app_id', 'api_secret', 'api_key']
        for key in required_keys:
            if key in config and config[key]:
                print(f"✓ {key} - 已配置")
            else:
                print(f"✗ {key} - 未配置")
                return False
        
        print("✓ 配置文件检查通过")
        return True
        
    except Exception as e:
        print(f"✗ 配置文件读取失败: {e}")
        return False

def test_audio_converter():
    """测试音频转换功能"""
    print("\n=== 测试音频转换功能 ===")
    
    try:
        from audio_converter import AudioConverter
        converter = AudioConverter()
        
        print(f"✓ 音频转换器初始化成功")
        print(f"  FFmpeg: {'可用' if converter.ffmpeg_available else '不可用'}")
        print(f"  Pydub: {'可用' if converter.pydub_available else '不可用'}")
        
        if not (converter.ffmpeg_available or converter.pydub_available):
            print("✗ 没有可用的音频转换工具")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 音频转换器测试失败: {e}")
        return False

def test_speech_evaluator():
    """测试语音评测器"""
    print("\n=== 测试语音评测器 ===")
    
    try:
        from speech_evaluator import XunfeiSpeechEvaluator
        
        # 读取配置
        with open("config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        evaluator = XunfeiSpeechEvaluator(
            config['app_id'],
            config['api_secret'],
            config['api_key']
        )
        
        print("✓ 语音评测器初始化成功")
        
        # 测试URL生成
        url = evaluator.create_url()
        if url.startswith("wss://"):
            print("✓ API URL生成成功")
        else:
            print("✗ API URL生成失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 语音评测器测试失败: {e}")
        return False

def test_result_analyzer():
    """测试结果分析器"""
    print("\n=== 测试结果分析器 ===")
    
    try:
        from result_analyzer import ResultAnalyzer
        analyzer = ResultAnalyzer()
        
        # 模拟测试数据
        mock_result = {
            "total_score": 85.5,
            "phone_score": 82.0,
            "tone_score": 88.0,
            "words": [
                {
                    "content": "测试",
                    "total_score": 85,
                    "syllables": [
                        {
                            "content": "测",
                            "total_score": 80,
                            "initial_score": 75,
                            "final_score": 85,
                            "tone_score": 80
                        }
                    ]
                }
            ]
        }
        
        result = analyzer.analyze(mock_result)
        
        if 'total_score' in result and result['total_score'] > 0:
            print("✓ 结果分析器测试成功")
            return True
        else:
            print("✗ 结果分析器测试失败")
            return False
        
    except Exception as e:
        print(f"✗ 结果分析器测试失败: {e}")
        return False

def test_audio_files():
    """测试音频文件"""
    print("\n=== 测试音频文件 ===")
    
    audio_dir = Path("音频")
    if not audio_dir.exists():
        print("✗ 音频目录不存在")
        return False
    
    audio_files = list(audio_dir.glob("*.MP3")) + list(audio_dir.glob("*.mp3"))
    
    if not audio_files:
        print("✗ 没有找到音频文件")
        return False
    
    print(f"✓ 找到 {len(audio_files)} 个音频文件")
    
    # 测试第一个音频文件
    test_file = audio_files[0]
    print(f"测试文件: {test_file.name}")
    
    try:
        from audio_converter import AudioConverter
        converter = AudioConverter()
        
        info = converter.get_audio_info(str(test_file))
        if 'error' not in info:
            print(f"✓ 音频文件信息获取成功: {info}")
            return True
        else:
            print(f"✗ 音频文件信息获取失败: {info['error']}")
            return False
            
    except Exception as e:
        print(f"✗ 音频文件测试失败: {e}")
        return False

def test_parsed_texts():
    """测试解析文本"""
    print("\n=== 测试解析文本 ===")
    
    if not os.path.exists("parsed_texts.json"):
        print("✗ parsed_texts.json 不存在")
        return False
    
    try:
        with open("parsed_texts.json", 'r', encoding='utf-8') as f:
            texts = json.load(f)
        
        if len(texts) > 0:
            print(f"✓ 找到 {len(texts)} 个测试文本")
            
            # 显示前3个文本的预览
            for i, (key, text) in enumerate(list(texts.items())[:3]):
                preview = text[:50] + "..." if len(text) > 50 else text
                print(f"  {key}: {preview}")
            
            return True
        else:
            print("✗ 测试文本为空")
            return False
            
    except Exception as e:
        print(f"✗ 测试文本读取失败: {e}")
        return False

def main():
    """主测试函数"""
    print("普通话评测系统 - 系统测试")
    print("=" * 50)
    
    tests = [
        ("依赖包", test_dependencies),
        ("配置文件", test_config),
        ("音频转换", test_audio_converter),
        ("语音评测", test_speech_evaluator),
        ("结果分析", test_result_analyzer),
        ("音频文件", test_audio_files),
        ("测试文本", test_parsed_texts)
    ]
    
    passed = 0
    total = len(tests)
    
    for name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"✗ {name}测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"测试完成: {passed}/{total} 项通过")
    
    if passed == total:
        print("✓ 所有测试通过，系统可以正常使用")
        print("\n启动方法:")
        print("1. 双击 run.bat")
        print("2. 或运行: python main.py")
    else:
        print("✗ 部分测试失败，请检查相关问题")
        
        if passed < 3:
            print("\n建议:")
            print("1. 运行 install.bat 安装依赖")
            print("2. 检查配置文件")
            print("3. 确保音频文件存在")

if __name__ == "__main__":
    main()
