# 普通话评测系统完整方案

## 一、市面上成熟的普通话测试API接口

### 1. 科大讯飞语音评测API ⭐⭐⭐⭐⭐
**优势：**
- 支持声母、韵母、声调三维度评分
- 支持单字、词语、句子、篇章四种题型
- 提供完整度分、流畅度分、声韵分、调型分
- 技术成熟，准确率高
- 文档完善，SDK支持多平台

**评测维度：**
- 总分(total_score)
- 声韵分(phone_score) - 声母和韵母正确率
- 调型分(tone_score) - 声调正确率
- 完整度分(integrity_score)
- 流畅度分(fluency_score)

**价格：** 按次计费，约0.1-0.3元/次

### 2. 腾讯云智聆口语评测(SOE) ⭐⭐⭐⭐
**优势：**
- 支持中英文评测
- 支持拼音级别评测
- 提供实时评测和离线评测
- 支持声母、韵母、声调评分

**评测维度：**
- 准确度评分
- 流畅度评分
- 完整度评分
- 发音评分

**价格：** 按次计费，约0.08-0.25元/次

### 3. 百度智能云语音评测 ⭐⭐⭐
**优势：**
- 集成度高
- 支持多种音频格式
- 提供详细的错误定位

**价格：** 按次计费，约0.1-0.2元/次

### 4. 阿里云智能语音评测 ⭐⭐⭐
**优势：**
- 云原生架构
- 支持大并发
- 提供多维度评分

**价格：** 按次计费，约0.1-0.2元/次

## 二、推荐方案：科大讯飞 + 自研补充

**主要原因：**
1. 科大讯飞在普通话评测领域技术最成熟
2. 提供最详细的声母、韵母、声调分析
3. 支持四种题型完整覆盖
4. 文档和SDK支持最完善

## 三、系统架构设计

### 3.1 整体架构
```
前端界面 → 后端API → 语音处理模块 → 科大讯飞API → 结果分析模块 → 可视化展示
```

### 3.2 核心模块

#### 1. 语音录制模块
- 支持实时录音
- 音频格式：16kHz, 16bit, 单声道PCM
- 支持音频文件上传

#### 2. 语音预处理模块
- 音频格式转换
- 降噪处理
- 音量标准化

#### 3. 评测引擎模块
- 集成科大讯飞API
- 支持四种题型：读字、读词、读句、自由说话
- 实时评测和批量评测

#### 4. 结果分析模块
- 声母准确率分析
- 韵母准确率分析
- 声调准确率分析
- 流畅度分析

#### 5. 可视化展示模块
- 文本标注显示
- 错误高亮
- 评分图表
- 详细报告生成

## 四、评测维度设计

### 4.1 四大评测维度
1. **声母评测 (25%权重)**
   - 检测声母发音准确性
   - 标注错误的声母

2. **韵母评测 (25%权重)**
   - 检测韵母发音准确性
   - 标注错误的韵母

3. **声调评测 (25%权重)**
   - 检测四声发音准确性
   - 标注声调错误

4. **流畅性评测 (25%权重)**
   - 语速适中性
   - 停顿合理性
   - 连读自然性

### 4.2 综合评分算法
```
综合得分 = 声母得分 × 0.25 + 韵母得分 × 0.25 + 声调得分 × 0.25 + 流畅度得分 × 0.25
```

## 五、四种测试类型实现

### 5.1 读字测试
- **题型**：单字朗读
- **评测重点**：声母、韵母、声调准确性
- **示例**：从您的50个音频文件中选取单字部分

### 5.2 读词语测试
- **题型**：双音节词语朗读
- **评测重点**：声调变化、轻声处理
- **示例**：普通话、学习、工作等

### 5.3 读文章测试
- **题型**：段落朗读
- **评测重点**：整体流畅性、语调自然性
- **示例**：使用您现有的parsed_texts.json中的文本

### 5.4 自由说话测试
- **题型**：命题说话
- **评测重点**：语言表达能力、发音稳定性
- **时长**：3-5分钟

## 六、错误标注显示方案

### 6.1 文本标注方式
```html
<span class="correct">正确发音</span>
<span class="error-initial">声母错误</span>
<span class="error-final">韵母错误</span>
<span class="error-tone">声调错误</span>
<span class="missing">漏读</span>
<span class="extra">多读</span>
```

### 6.2 颜色编码
- 绿色：正确发音
- 红色：声母错误
- 蓝色：韵母错误
- 橙色：声调错误
- 灰色：漏读
- 紫色：多读

### 6.3 详细错误信息
- 显示期望发音和实际发音
- 提供发音指导建议
- 播放标准发音示例

## 七、技术实现栈

### 7.1 前端技术
- **框架**：React.js / Vue.js
- **音频处理**：Web Audio API
- **UI组件**：Ant Design / Element UI
- **图表**：ECharts / Chart.js

### 7.2 后端技术
- **语言**：Python
- **框架**：FastAPI / Flask
- **音频处理**：librosa, pydub
- **数据库**：PostgreSQL / MongoDB
- **缓存**：Redis

### 7.3 部署方案
- **容器化**：Docker
- **云服务**：阿里云 / 腾讯云
- **CDN**：音频文件加速
- **负载均衡**：Nginx

## 八、开发计划

### 阶段一：基础功能开发 (2周)
1. 音频录制和上传功能
2. 科大讯飞API集成
3. 基础评测功能

### 阶段二：核心功能完善 (3周)
1. 四种题型实现
2. 错误标注显示
3. 评分算法优化

### 阶段三：高级功能开发 (2周)
1. 详细报告生成
2. 历史记录管理
3. 用户管理系统

### 阶段四：测试和优化 (1周)
1. 功能测试
2. 性能优化
3. 用户体验优化

## 九、成本预估

### 9.1 API调用成本
- 科大讯飞API：约0.2元/次
- 预计日调用量：1000次
- 月成本：约6000元

### 9.2 服务器成本
- 云服务器：约500元/月
- 数据库：约200元/月
- CDN：约100元/月

### 9.3 总成本
- 月运营成本：约6800元
- 开发成本：约8周开发时间

## 十、后续优化方向

1. **AI模型自研**：减少API依赖，降低成本
2. **多方言支持**：扩展到其他方言评测
3. **移动端适配**：开发手机APP
4. **教学功能**：增加发音教学和练习功能
5. **数据分析**：用户学习轨迹分析和个性化推荐
