# 普通话评测系统

一个基于科大讯飞API的普通话语音评测系统，支持四种题型的评测和详细的发音分析。

## 功能特点

### 🎯 四种评测题型
- **读字测试**：单字发音评测，重点检测声母、韵母、声调
- **读词测试**：词语发音评测，检测声调变化和轻声处理
- **读句测试**：句子朗读评测，评估整体流畅性和语调
- **自由说话**：篇章朗读评测，综合评估语言表达能力

### 📊 多维度评分
- **声母评测**：检测声母发音准确性
- **韵母评测**：检测韵母发音准确性  
- **声调评测**：检测四声发音准确性
- **流畅性评测**：评估语速、停顿合理性
- **完整性评测**：检测漏读、多读情况

### 🎨 直观的结果展示
- **文本标注**：直接在文本上标注错误位置
- **颜色编码**：不同颜色表示不同类型的错误
- **详细分析**：提供具体的改进建议
- **评分报告**：生成完整的评测报告

### 🎙️ 音频功能
- **实时录音**：支持麦克风实时录音
- **文件导入**：支持WAV、MP3、M4A等格式
- **波形显示**：实时显示音频波形
- **格式转换**：自动转换为标准格式

## 安装说明

### 1. 环境要求
- Python 3.7+
- Windows/macOS/Linux

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 音频处理依赖（可选）
如果需要处理MP3等格式，请安装：
```bash
# 安装pydub
pip install pydub

# 安装ffmpeg（用于音频格式转换）
# Windows: 下载ffmpeg并添加到PATH
# macOS: brew install ffmpeg  
# Linux: sudo apt-get install ffmpeg
```

## 配置说明

### 1. 获取API密钥
1. 注册科大讯飞开放平台账号：https://www.xfyun.cn/
2. 创建语音评测应用
3. 获取APPID、API Secret、API Key

### 2. 配置API密钥
启动程序后，点击"API配置"按钮，输入您的API密钥信息。

或者直接编辑 `config.json` 文件：
```json
{
  "app_id": "your_app_id",
  "api_secret": "your_api_secret", 
  "api_key": "your_api_key"
}
```

## 使用方法

### 1. 启动程序
```bash
python main.py
```

### 2. 基本使用流程
1. **配置API**：首次使用需要配置科大讯飞API密钥
2. **选择题型**：选择读字、读词、读句或自由说话
3. **输入文本**：输入或选择要评测的文本内容
4. **录制音频**：点击"开始录音"进行录音，或加载音频文件
5. **开始评测**：点击"开始评测"获取结果
6. **查看结果**：查看评分和详细分析

### 3. 题型说明

#### 读字测试
- 适用于单字发音练习
- 重点评测声母、韵母、声调准确性
- 示例文本：中、华、人、民

#### 读词测试  
- 适用于词语发音练习
- 评测声调变化、轻声处理
- 示例文本：普通话、学习、工作

#### 读句测试
- 适用于句子朗读练习
- 评测语调、停顿、连读
- 示例文本：我爱学习普通话

#### 自由说话
- 适用于篇章朗读或自由表达
- 综合评测语言表达能力
- 支持较长文本内容

## 结果说明

### 评分标准
- **90-100分**：优秀，发音标准
- **80-89分**：良好，基本标准
- **70-79分**：中等，有待改进
- **60-69分**：及格，需要练习
- **60分以下**：不及格，需要重点练习

### 错误标注
- 🟢 **绿色**：发音正确
- 🔴 **红色**：声母错误
- 🔵 **蓝色**：韵母错误  
- 🟠 **橙色**：声调错误
- ⚪ **灰色**：漏读
- 🟣 **紫色**：多读

### 普通话等级对应
- **一级甲等**：97-100分
- **一级乙等**：92-96分
- **二级甲等**：87-91分
- **二级乙等**：80-86分
- **三级甲等**：70-79分
- **三级乙等**：60-69分

## 文件结构

```
普通话测评/
├── main.py                 # 主程序入口
├── speech_evaluator.py     # 科大讯飞API封装
├── audio_recorder.py       # 音频录制模块
├── result_analyzer.py      # 结果分析模块
├── config.json            # 配置文件
├── requirements.txt       # 依赖包列表
├── parsed_texts.json      # 预设测试文本
├── README.md             # 说明文档
└── 音频/                  # 音频文件目录
    ├── 01.MP3
    ├── 02.MP3
    └── ...
```

## 常见问题

### Q: 录音没有声音怎么办？
A: 
1. 检查麦克风是否正常工作
2. 检查系统音频权限设置
3. 运行麦克风测试功能
4. 确认音频设备选择正确

### Q: 评测失败怎么办？
A:
1. 检查网络连接是否正常
2. 确认API密钥配置正确
3. 检查音频文件格式是否支持
4. 查看错误信息进行排查

### Q: 支持哪些音频格式？
A:
- 原生支持：WAV格式（16kHz, 单声道, 16bit）
- 扩展支持：MP3, M4A等（需要安装pydub和ffmpeg）

### Q: 如何提高评测准确性？
A:
1. 使用质量好的麦克风
2. 在安静的环境中录音
3. 保持适当的录音音量
4. 发音清晰，语速适中

## 技术支持

如有问题，请：
1. 查看本文档的常见问题部分
2. 检查科大讯飞官方文档
3. 提交Issue或联系开发者

## 许可证

本项目仅供学习和研究使用。使用科大讯飞API需要遵守其服务条款。

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 支持四种评测题型
- 实现GUI界面
- 集成科大讯飞API
- 支持音频录制和文件导入
- 提供详细的结果分析
