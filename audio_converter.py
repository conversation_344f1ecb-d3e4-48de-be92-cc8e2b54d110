#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频格式转换模块
支持多种音频格式转换为标准WAV格式
"""

import os
import tempfile
import subprocess
import wave
import numpy as np
from pathlib import Path

class AudioConverter:
    def __init__(self):
        """初始化音频转换器"""
        self.target_sample_rate = 16000
        self.target_channels = 1
        self.target_sample_width = 2  # 16bit
        
        # 检查可用的转换工具
        self.ffmpeg_available = self._check_ffmpeg()
        self.pydub_available = self._check_pydub()
        
        print(f"音频转换工具状态:")
        print(f"  FFmpeg: {'可用' if self.ffmpeg_available else '不可用'}")
        print(f"  Pydub: {'可用' if self.pydub_available else '不可用'}")
    
    def _check_ffmpeg(self):
        """检查FFmpeg是否可用"""
        try:
            result = subprocess.run(['ffmpeg', '-version'], 
                                  capture_output=True, text=True)
            return result.returncode == 0
        except FileNotFoundError:
            return False
    
    def _check_pydub(self):
        """检查Pydub是否可用"""
        try:
            import pydub
            return True
        except ImportError:
            return False
    
    def convert_to_standard_format(self, input_file, output_file=None):
        """
        转换音频文件为标准格式 (16kHz, 单声道, 16bit WAV)
        
        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径，如果为None则自动生成
            
        Returns:
            str: 输出文件路径，失败返回None
        """
        if not os.path.exists(input_file):
            raise Exception(f"输入文件不存在: {input_file}")
        
        # 如果已经是WAV格式，检查是否符合标准
        if input_file.lower().endswith('.wav'):
            if self._check_wav_format(input_file):
                print(f"音频文件已是标准格式: {input_file}")
                return input_file
        
        # 生成输出文件名
        if output_file is None:
            input_path = Path(input_file)
            output_file = str(input_path.parent / f"{input_path.stem}_converted.wav")
        
        # 尝试不同的转换方法
        success = False
        
        # 方法1: 使用FFmpeg
        if self.ffmpeg_available and not success:
            success = self._convert_with_ffmpeg(input_file, output_file)
        
        # 方法2: 使用Pydub
        if self.pydub_available and not success:
            success = self._convert_with_pydub(input_file, output_file)
        
        # 方法3: 如果是WAV文件，尝试直接处理
        if input_file.lower().endswith('.wav') and not success:
            success = self._convert_wav_format(input_file, output_file)
        
        if success and os.path.exists(output_file):
            print(f"音频转换成功: {output_file}")
            return output_file
        else:
            raise Exception("所有转换方法都失败了，请检查音频文件格式或安装相关依赖")
    
    def _check_wav_format(self, wav_file):
        """检查WAV文件是否符合标准格式"""
        try:
            with wave.open(wav_file, 'rb') as wav:
                channels = wav.getnchannels()
                sample_width = wav.getsampwidth()
                framerate = wav.getframerate()
                
                return (channels == self.target_channels and 
                       sample_width == self.target_sample_width and 
                       framerate == self.target_sample_rate)
        except:
            return False
    
    def _convert_with_ffmpeg(self, input_file, output_file):
        """使用FFmpeg转换音频"""
        try:
            cmd = [
                'ffmpeg', '-i', input_file,
                '-ar', str(self.target_sample_rate),  # 采样率
                '-ac', str(self.target_channels),     # 声道数
                '-sample_fmt', 's16',                 # 16bit
                '-y', output_file                     # 覆盖输出文件
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"FFmpeg转换成功: {input_file} -> {output_file}")
                return True
            else:
                print(f"FFmpeg转换失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"FFmpeg转换异常: {e}")
            return False
    
    def _convert_with_pydub(self, input_file, output_file):
        """使用Pydub转换音频"""
        try:
            from pydub import AudioSegment
            
            # 加载音频文件
            audio = AudioSegment.from_file(input_file)
            
            # 转换格式
            audio = audio.set_frame_rate(self.target_sample_rate)
            audio = audio.set_channels(self.target_channels)
            audio = audio.set_sample_width(self.target_sample_width)
            
            # 导出为WAV
            audio.export(output_file, format="wav")
            
            print(f"Pydub转换成功: {input_file} -> {output_file}")
            return True
            
        except Exception as e:
            print(f"Pydub转换失败: {e}")
            return False
    
    def _convert_wav_format(self, input_file, output_file):
        """转换WAV文件格式"""
        try:
            with wave.open(input_file, 'rb') as input_wav:
                # 读取原始数据
                frames = input_wav.readframes(-1)
                channels = input_wav.getnchannels()
                sample_width = input_wav.getsampwidth()
                framerate = input_wav.getframerate()
                
                print(f"原始格式: {channels}声道, {sample_width*8}bit, {framerate}Hz")
                
                # 转换为numpy数组
                if sample_width == 1:
                    dtype = np.uint8
                elif sample_width == 2:
                    dtype = np.int16
                elif sample_width == 4:
                    dtype = np.int32
                else:
                    raise Exception(f"不支持的采样位宽: {sample_width}")
                
                audio_data = np.frombuffer(frames, dtype=dtype)
                
                # 处理多声道
                if channels > 1:
                    audio_data = audio_data.reshape(-1, channels)
                    # 转换为单声道（取平均值）
                    audio_data = np.mean(audio_data, axis=1).astype(dtype)
                
                # 重采样
                if framerate != self.target_sample_rate:
                    # 简单的重采样（线性插值）
                    old_length = len(audio_data)
                    new_length = int(old_length * self.target_sample_rate / framerate)
                    
                    old_indices = np.linspace(0, old_length - 1, old_length)
                    new_indices = np.linspace(0, old_length - 1, new_length)
                    audio_data = np.interp(new_indices, old_indices, audio_data).astype(dtype)
                
                # 转换采样位宽
                if sample_width != self.target_sample_width:
                    if sample_width == 1 and self.target_sample_width == 2:
                        # 8bit -> 16bit
                        audio_data = ((audio_data.astype(np.float32) - 128) * 256).astype(np.int16)
                    elif sample_width == 4 and self.target_sample_width == 2:
                        # 32bit -> 16bit
                        audio_data = (audio_data / 65536).astype(np.int16)
                
                # 保存转换后的文件
                with wave.open(output_file, 'wb') as output_wav:
                    output_wav.setnchannels(self.target_channels)
                    output_wav.setsampwidth(self.target_sample_width)
                    output_wav.setframerate(self.target_sample_rate)
                    output_wav.writeframes(audio_data.tobytes())
                
                print(f"WAV格式转换成功: {input_file} -> {output_file}")
                return True
                
        except Exception as e:
            print(f"WAV格式转换失败: {e}")
            return False
    
    def get_audio_info(self, audio_file):
        """获取音频文件信息"""
        try:
            if audio_file.lower().endswith('.wav'):
                with wave.open(audio_file, 'rb') as wav:
                    return {
                        'format': 'WAV',
                        'channels': wav.getnchannels(),
                        'sample_width': wav.getsampwidth(),
                        'framerate': wav.getframerate(),
                        'frames': wav.getnframes(),
                        'duration': wav.getnframes() / wav.getframerate()
                    }
            elif self.pydub_available:
                from pydub import AudioSegment
                audio = AudioSegment.from_file(audio_file)
                return {
                    'format': audio_file.split('.')[-1].upper(),
                    'channels': audio.channels,
                    'sample_width': audio.sample_width,
                    'framerate': audio.frame_rate,
                    'duration': len(audio) / 1000.0
                }
            else:
                return {'error': '无法获取音频信息，需要安装pydub'}
                
        except Exception as e:
            return {'error': f'获取音频信息失败: {str(e)}'}
    
    def test_conversion(self, test_file=None):
        """测试音频转换功能"""
        if test_file is None:
            print("请提供测试音频文件路径")
            return False
        
        if not os.path.exists(test_file):
            print(f"测试文件不存在: {test_file}")
            return False
        
        try:
            print(f"测试音频转换: {test_file}")
            
            # 获取原始信息
            original_info = self.get_audio_info(test_file)
            print(f"原始格式: {original_info}")
            
            # 转换
            converted_file = self.convert_to_standard_format(test_file)
            
            if converted_file:
                # 获取转换后信息
                converted_info = self.get_audio_info(converted_file)
                print(f"转换后格式: {converted_info}")
                
                # 清理临时文件
                if converted_file != test_file:
                    os.unlink(converted_file)
                
                return True
            else:
                return False
                
        except Exception as e:
            print(f"测试失败: {e}")
            return False

# 示例用法
if __name__ == "__main__":
    converter = AudioConverter()
    
    # 测试转换功能
    test_files = [
        "test.mp3",
        "test.m4a", 
        "test.wav"
    ]
    
    for test_file in test_files:
        if os.path.exists(test_file):
            print(f"\n测试文件: {test_file}")
            converter.test_conversion(test_file)
        else:
            print(f"测试文件不存在: {test_file}")
