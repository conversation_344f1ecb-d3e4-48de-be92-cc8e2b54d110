@echo off
chcp 65001
echo ========================================
echo 普通话评测系统 - 安装脚本
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python环境
    echo 请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

echo 正在安装依赖包...
pip install numpy websocket-client pydub scipy

if errorlevel 1 (
    echo.
    echo 警告: 部分依赖包安装失败
    echo 请检查网络连接或Python环境
    echo.
) else (
    echo.
    echo 依赖包安装完成！
    echo.
)

echo 正在检查音频处理库...
python -c "import pydub; print('音频处理库检查通过')" 2>nul
if errorlevel 1 (
    echo 警告: pydub库检查失败，可能影响音频格式转换
    echo.
)

echo ========================================
echo 安装完成！
echo ========================================
echo.
echo 使用方法：
echo 1. API密钥已预配置，可直接使用
echo 2. 运行程序: python main.py
echo 3. 或者双击 run.bat 启动程序
echo 4. 选择音频文件进行评测（支持WAV/MP3/M4A等格式）
echo.
echo 推荐安装ffmpeg以获得更好的音频格式支持：
echo https://ffmpeg.org/download.html
echo.
pause
