#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
普通话评测系统 - 主程序
支持四种题型：读字、读词、读句、自由说话
"""

import sys
import os
import json
import threading
import time
from datetime import datetime
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import pyaudio
import wave
import numpy as np
from pathlib import Path

# 导入自定义模块
from speech_evaluator import XunfeiSpeechEvaluator
from audio_converter import AudioConverter
from result_analyzer import ResultAnalyzer

class PutonghuaEvaluationGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("普通话评测系统")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # 初始化组件
        self.audio_converter = AudioConverter()
        self.result_analyzer = ResultAnalyzer()
        self.evaluator = None

        # 状态变量
        self.current_audio_file = None
        self.evaluation_result = None
        
        # 加载配置
        self.load_config()
        
        # 创建界面
        self.create_widgets()
        
        # 加载测试文本
        self.load_test_texts()

        # 初始化API
        self.init_evaluator()
    
    def load_config(self):
        """加载配置文件"""
        config_file = "config.json"
        default_config = {
            "app_id": "",
            "api_secret": "",
            "api_key": "",
            "audio_format": {
                "sample_rate": 16000,
                "channels": 1,
                "sample_width": 2
            }
        }
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            except:
                self.config = default_config
        else:
            self.config = default_config
            self.save_config()
    
    def save_config(self):
        """保存配置文件"""
        with open("config.json", 'w', encoding='utf-8') as f:
            json.dump(self.config, f, ensure_ascii=False, indent=2)

    def init_evaluator(self):
        """初始化评测器"""
        if all([self.config.get('app_id'), self.config.get('api_secret'), self.config.get('api_key')]):
            try:
                self.evaluator = XunfeiSpeechEvaluator(
                    self.config['app_id'],
                    self.config['api_secret'],
                    self.config['api_key']
                )
                self.status_label.config(text="API已配置，可以开始评测")
                print("科大讯飞API初始化成功")
            except Exception as e:
                self.status_label.config(text=f"API初始化失败: {str(e)}")
                print(f"API初始化失败: {e}")
        else:
            self.status_label.config(text="请配置API密钥")
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="普通话评测系统", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 左侧控制面板
        self.create_control_panel(main_frame)
        
        # 中间测试区域
        self.create_test_area(main_frame)
        
        # 右侧结果显示
        self.create_result_area(main_frame)
        
        # 底部状态栏
        self.create_status_bar(main_frame)
    
    def create_control_panel(self, parent):
        """创建左侧控制面板"""
        control_frame = ttk.LabelFrame(parent, text="控制面板", padding="10")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), 
                          padx=(0, 10))
        
        # API配置按钮
        ttk.Button(control_frame, text="API配置", 
                  command=self.open_config_dialog).grid(row=0, column=0, 
                                                       sticky=tk.W+tk.E, pady=2)
        
        # 题型选择
        ttk.Label(control_frame, text="题型选择:").grid(row=1, column=0, 
                                                    sticky=tk.W, pady=(10, 2))
        
        self.test_type = tk.StringVar(value="read_syllable")
        test_types = [
            ("读字", "read_syllable"),
            ("读词", "read_word"),
            ("读句", "read_sentence"),
            ("自由说话", "read_chapter")
        ]
        
        for i, (text, value) in enumerate(test_types):
            ttk.Radiobutton(control_frame, text=text, variable=self.test_type,
                           value=value, command=self.on_test_type_change).grid(
                           row=2+i, column=0, sticky=tk.W, pady=1)
        
        # 文件操作
        ttk.Label(control_frame, text="音频文件:").grid(row=7, column=0,
                                                    sticky=tk.W, pady=(20, 5))

        ttk.Button(control_frame, text="选择音频文件",
                  command=self.load_audio_file).grid(row=8, column=0,
                                                    sticky=tk.W+tk.E, pady=2)
        
        # 评测按钮
        self.evaluate_button = ttk.Button(control_frame, text="开始评测",
                                         command=self.start_evaluation,
                                         state='disabled')
        self.evaluate_button.grid(row=9, column=0, sticky=tk.W+tk.E, pady=10)

        # 清除结果
        ttk.Button(control_frame, text="清除结果",
                  command=self.clear_results).grid(row=10, column=0,
                                                  sticky=tk.W+tk.E, pady=2)
    
    def create_test_area(self, parent):
        """创建中间测试区域"""
        test_frame = ttk.LabelFrame(parent, text="测试内容", padding="10")
        test_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        test_frame.columnconfigure(0, weight=1)
        test_frame.rowconfigure(1, weight=1)
        
        # 测试文本输入
        ttk.Label(test_frame, text="测试文本:").grid(row=0, column=0, 
                                                  sticky=tk.W, pady=(0, 5))
        
        self.test_text = scrolledtext.ScrolledText(test_frame, height=8, 
                                                  font=('Arial', 12))
        self.test_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), 
                           pady=(0, 10))
        
        # 预设文本选择
        preset_frame = ttk.Frame(test_frame)
        preset_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(preset_frame, text="预设文本:").grid(row=0, column=0, sticky=tk.W)
        
        self.preset_combo = ttk.Combobox(preset_frame, state="readonly")
        self.preset_combo.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
        self.preset_combo.bind('<<ComboboxSelected>>', self.on_preset_selected)
        preset_frame.columnconfigure(1, weight=1)
        
        # 音频波形显示区域
        self.create_waveform_display(test_frame)
    
    def create_waveform_display(self, parent):
        """创建音频波形显示"""
        wave_frame = ttk.LabelFrame(parent, text="音频波形", padding="5")
        wave_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        wave_frame.columnconfigure(0, weight=1)
        
        # 使用Canvas显示波形
        self.wave_canvas = tk.Canvas(wave_frame, height=100, bg='white')
        self.wave_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=5)
        
        # 音频信息标签
        self.audio_info_label = ttk.Label(wave_frame, text="未加载音频")
        self.audio_info_label.grid(row=1, column=0, sticky=tk.W)
    
    def create_result_area(self, parent):
        """创建右侧结果显示区域"""
        result_frame = ttk.LabelFrame(parent, text="评测结果", padding="10")
        result_frame.grid(row=1, column=2, sticky=(tk.W, tk.E, tk.N, tk.S), 
                         padx=(10, 0))
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(1, weight=1)
        
        # 评分显示
        score_frame = ttk.Frame(result_frame)
        score_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        score_frame.columnconfigure(1, weight=1)
        
        # 总分
        ttk.Label(score_frame, text="总分:", font=('Arial', 10, 'bold')).grid(
            row=0, column=0, sticky=tk.W)
        self.total_score_label = ttk.Label(score_frame, text="--", 
                                          font=('Arial', 14, 'bold'), 
                                          foreground='red')
        self.total_score_label.grid(row=0, column=1, sticky=tk.E)
        
        # 各项得分
        score_items = [
            ("声母得分:", "initial_score"),
            ("韵母得分:", "final_score"),
            ("声调得分:", "tone_score"),
            ("流畅度:", "fluency_score"),
            ("完整度:", "integrity_score")
        ]
        
        self.score_labels = {}
        for i, (text, key) in enumerate(score_items):
            ttk.Label(score_frame, text=text).grid(row=i+1, column=0, sticky=tk.W)
            label = ttk.Label(score_frame, text="--")
            label.grid(row=i+1, column=1, sticky=tk.E)
            self.score_labels[key] = label
        
        # 详细结果显示
        ttk.Label(result_frame, text="详细分析:").grid(row=1, column=0, 
                                                   sticky=tk.W, pady=(10, 5))
        
        self.result_text = scrolledtext.ScrolledText(result_frame, height=20, 
                                                    font=('Consolas', 10))
        self.result_text.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置文本标签样式
        self.result_text.tag_configure("correct", foreground="green")
        self.result_text.tag_configure("error_initial", foreground="red", background="yellow")
        self.result_text.tag_configure("error_final", foreground="blue", background="yellow")
        self.result_text.tag_configure("error_tone", foreground="orange", background="yellow")
        self.result_text.tag_configure("missing", foreground="gray", background="lightgray")
        self.result_text.tag_configure("extra", foreground="purple", background="yellow")
    
    def create_status_bar(self, parent):
        """创建底部状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), 
                         pady=(10, 0))
        status_frame.columnconfigure(0, weight=1)
        
        self.status_label = ttk.Label(status_frame, text="就绪")
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        # 进度条
        self.progress = ttk.Progressbar(status_frame, mode='indeterminate')
        self.progress.grid(row=0, column=1, sticky=tk.E, padx=(10, 0))
    
    def load_test_texts(self):
        """加载测试文本"""
        try:
            # 加载parsed_texts.json中的文本
            if os.path.exists("parsed_texts.json"):
                with open("parsed_texts.json", 'r', encoding='utf-8') as f:
                    texts = json.load(f)
                
                # 更新下拉框
                text_options = []
                for key, text in texts.items():
                    # 截取前30个字符作为显示
                    display_text = text[:30] + "..." if len(text) > 30 else text
                    text_options.append(f"{key}: {display_text}")
                
                self.preset_combo['values'] = text_options
                self.parsed_texts = texts
            else:
                # 默认测试文本
                default_texts = {
                    "单字": "中华人民共和国",
                    "词语": "普通话 学习 工作 生活",
                    "句子": "我爱学习普通话，说好普通话是我们的责任。",
                    "篇章": "春天来了，大地复苏，万物生长。小草从土里钻出来，嫩嫩的，绿绿的。"
                }
                self.preset_combo['values'] = list(default_texts.keys())
                self.parsed_texts = default_texts
        except Exception as e:
            messagebox.showerror("错误", f"加载测试文本失败: {str(e)}")
    
    def on_test_type_change(self):
        """题型改变事件"""
        test_type = self.test_type.get()

        # 根据题型设置默认文本
        default_texts = {
            "read_syllable": "中",
            "read_word": "普通话",
            "read_sentence": "我爱学习普通话",
            "read_chapter": "春天来了，大地复苏，万物生长。"
        }

        # 音频时长建议
        duration_tips = {
            "read_syllable": "建议音频时长：3秒以内",
            "read_word": "建议音频时长：5秒以内",
            "read_sentence": "建议音频时长：10秒以内",
            "read_chapter": "建议音频时长：60秒以内"
        }

        if test_type in default_texts:
            self.test_text.delete(1.0, tk.END)
            self.test_text.insert(1.0, default_texts[test_type])

        # 更新状态提示
        if test_type in duration_tips:
            self.status_label.config(text=duration_tips[test_type])
    
    def on_preset_selected(self, event):
        """预设文本选择事件"""
        selection = self.preset_combo.get()
        if selection and ':' in selection:
            key = selection.split(':')[0]
            if key in self.parsed_texts:
                self.test_text.delete(1.0, tk.END)
                self.test_text.insert(1.0, self.parsed_texts[key])
    
    def open_config_dialog(self):
        """打开API配置对话框"""
        config_window = tk.Toplevel(self.root)
        config_window.title("API配置")
        config_window.geometry("400x300")
        config_window.transient(self.root)
        config_window.grab_set()
        
        # 配置表单
        ttk.Label(config_window, text="科大讯飞API配置", 
                 font=('Arial', 12, 'bold')).pack(pady=10)
        
        # APP ID
        ttk.Label(config_window, text="APP ID:").pack(anchor=tk.W, padx=20)
        app_id_entry = ttk.Entry(config_window, width=50)
        app_id_entry.pack(padx=20, pady=5)
        app_id_entry.insert(0, self.config.get('app_id', ''))
        
        # API Secret
        ttk.Label(config_window, text="API Secret:").pack(anchor=tk.W, padx=20)
        api_secret_entry = ttk.Entry(config_window, width=50, show='*')
        api_secret_entry.pack(padx=20, pady=5)
        api_secret_entry.insert(0, self.config.get('api_secret', ''))
        
        # API Key
        ttk.Label(config_window, text="API Key:").pack(anchor=tk.W, padx=20)
        api_key_entry = ttk.Entry(config_window, width=50, show='*')
        api_key_entry.pack(padx=20, pady=5)
        api_key_entry.insert(0, self.config.get('api_key', ''))
        
        # 按钮
        button_frame = ttk.Frame(config_window)
        button_frame.pack(pady=20)
        
        def save_config():
            self.config['app_id'] = app_id_entry.get()
            self.config['api_secret'] = api_secret_entry.get()
            self.config['api_key'] = api_key_entry.get()
            self.save_config()
            
            # 重新初始化评测器
            self.init_evaluator()
            messagebox.showinfo("成功", "API配置已保存")
            
            config_window.destroy()
        
        ttk.Button(button_frame, text="保存", command=save_config).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=config_window.destroy).pack(side=tk.LEFT, padx=5)
    

    
    def load_audio_file(self):
        """加载音频文件"""
        file_path = filedialog.askopenfilename(
            title="选择音频文件",
            filetypes=[
                ("音频文件", "*.wav *.mp3 *.m4a *.flac *.aac *.ogg *.wma"),
                ("WAV文件", "*.wav"),
                ("MP3文件", "*.mp3"),
                ("M4A文件", "*.m4a"),
                ("FLAC文件", "*.flac"),
                ("AAC文件", "*.aac"),
                ("OGG文件", "*.ogg"),
                ("WMA文件", "*.wma"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            try:
                # 转换音频格式为标准格式
                self.status_label.config(text="正在处理音频文件...")
                converted_file = self.audio_converter.convert_to_standard_format(file_path)

                if converted_file:
                    self.current_audio_file = converted_file
                    self.display_waveform(converted_file)
                    self.evaluate_button.config(state='normal')
                    self.audio_info_label.config(text=f"音频文件: {os.path.basename(file_path)}")
                    self.status_label.config(text="音频文件加载成功")
                else:
                    messagebox.showerror("错误", "音频文件格式转换失败")
                    self.status_label.config(text="音频文件加载失败")
            except Exception as e:
                messagebox.showerror("错误", f"加载音频文件失败: {str(e)}")
                self.status_label.config(text="音频文件加载失败")
    
    def display_waveform(self, audio_file):
        """显示音频波形"""
        try:
            # 清除之前的波形
            self.wave_canvas.delete("all")
            
            # 读取音频文件
            if audio_file.endswith('.wav'):
                with wave.open(audio_file, 'rb') as wav_file:
                    frames = wav_file.readframes(-1)
                    sound_info = pyaudio.paInt16
                    audio_data = np.frombuffer(frames, dtype=np.int16)
            else:
                # 对于其他格式，这里需要使用其他库如librosa
                self.wave_canvas.create_text(150, 50, text="不支持的音频格式", fill="red")
                return
            
            # 绘制波形
            canvas_width = self.wave_canvas.winfo_width()
            canvas_height = self.wave_canvas.winfo_height()
            
            if canvas_width <= 1:  # 如果canvas还没有渲染
                self.root.after(100, lambda: self.display_waveform(audio_file))
                return
            
            # 采样数据以适应canvas宽度
            if len(audio_data) > canvas_width:
                step = len(audio_data) // canvas_width
                audio_data = audio_data[::step]
            
            # 归一化
            if len(audio_data) > 0:
                audio_data = audio_data / np.max(np.abs(audio_data))
                
                # 绘制波形
                for i in range(len(audio_data) - 1):
                    x1 = i * canvas_width / len(audio_data)
                    y1 = canvas_height / 2 - audio_data[i] * canvas_height / 2
                    x2 = (i + 1) * canvas_width / len(audio_data)
                    y2 = canvas_height / 2 - audio_data[i + 1] * canvas_height / 2
                    
                    self.wave_canvas.create_line(x1, y1, x2, y2, fill="blue", width=1)
            
        except Exception as e:
            self.wave_canvas.create_text(150, 50, text=f"波形显示错误: {str(e)}", fill="red")
    
    def start_evaluation(self):
        """开始评测"""
        if not self.evaluator:
            messagebox.showerror("错误", "请先配置API密钥")
            return
        
        if not self.current_audio_file:
            messagebox.showerror("错误", "请先加载音频文件")
            return
        
        test_text = self.test_text.get(1.0, tk.END).strip()
        if not test_text:
            messagebox.showerror("错误", "请输入测试文本")
            return
        
        # 禁用评测按钮
        self.evaluate_button.config(state='disabled')
        self.progress.start()
        self.status_label.config(text="正在评测...")
        
        # 在新线程中执行评测
        threading.Thread(target=self._evaluate_thread, 
                        args=(test_text, self.test_type.get())).start()
    
    def _evaluate_thread(self, text, category):
        """评测线程"""
        try:
            result, error = self.evaluator.evaluate(
                text=text,
                category=category,
                audio_file=self.current_audio_file
            )
            
            # 在主线程中更新界面
            self.root.after(0, self._evaluation_complete, result, error)
            
        except Exception as e:
            self.root.after(0, self._evaluation_complete, None, str(e))
    
    def _evaluation_complete(self, result, error):
        """评测完成回调"""
        self.progress.stop()
        self.evaluate_button.config(state='normal')
        
        if error:
            self.status_label.config(text=f"评测失败: {error}")
            messagebox.showerror("评测失败", error)
        elif result:
            self.status_label.config(text="评测完成")
            self.evaluation_result = result
            self.display_results(result)
        else:
            self.status_label.config(text="评测失败: 未知错误")
            messagebox.showerror("评测失败", "未知错误")
    
    def display_results(self, result):
        """显示评测结果"""
        try:
            # 解析结果
            analyzed_result = self.result_analyzer.analyze(result)
            
            # 更新评分显示
            if 'total_score' in analyzed_result:
                self.total_score_label.config(text=f"{analyzed_result['total_score']:.1f}")
            
            # 更新各项得分
            for key, label in self.score_labels.items():
                if key in analyzed_result:
                    label.config(text=f"{analyzed_result[key]:.1f}")
            
            # 显示详细结果
            self.result_text.delete(1.0, tk.END)
            
            # 显示文本标注结果
            if 'annotated_text' in analyzed_result:
                self._display_annotated_text(analyzed_result['annotated_text'])
            
            # 显示详细分析
            if 'detailed_analysis' in analyzed_result:
                self.result_text.insert(tk.END, "\n\n=== 详细分析 ===\n")
                self.result_text.insert(tk.END, analyzed_result['detailed_analysis'])
            
        except Exception as e:
            messagebox.showerror("错误", f"显示结果失败: {str(e)}")
    
    def _display_annotated_text(self, annotated_text):
        """显示标注文本"""
        self.result_text.insert(tk.END, "=== 发音标注 ===\n")
        
        for item in annotated_text:
            text = item['text']
            tag = item.get('tag', 'correct')
            
            start_pos = self.result_text.index(tk.INSERT)
            self.result_text.insert(tk.END, text)
            end_pos = self.result_text.index(tk.INSERT)
            
            if tag != 'correct':
                self.result_text.tag_add(tag, start_pos, end_pos)
    
    def clear_results(self):
        """清除结果"""
        # 清除评分显示
        self.total_score_label.config(text="--")
        for label in self.score_labels.values():
            label.config(text="--")

        # 清除详细结果
        self.result_text.delete(1.0, tk.END)

        # 清除音频
        self.current_audio_file = None
        self.wave_canvas.delete("all")
        self.audio_info_label.config(text="未加载音频")

        # 重置按钮状态
        self.evaluate_button.config(state='disabled')
        if self.evaluator:
            self.status_label.config(text="API已配置，可以开始评测")
        else:
            self.status_label.config(text="请配置API密钥")

def main():
    """主函数"""
    root = tk.Tk()
    app = PutonghuaEvaluationGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
