@echo off
chcp 65001 >nul
set PYTHONIOENCODING=utf-8
title 普通话评测系统

echo ========================================
echo 普通话评测系统 - 修复版本
echo ========================================
echo.

echo 正在测试API连接...
python test_api_connection.py

if errorlevel 1 (
    echo.
    echo API测试失败，请检查网络连接和配置
    pause
    exit /b 1
)

echo.
echo API测试通过，正在启动主程序...
python main.py

if errorlevel 1 (
    echo.
    echo 程序运行出错！
    echo 请检查：
    echo 1. Python环境是否正确安装
    echo 2. 依赖包是否安装完整
    echo 3. 音频文件是否存在
    echo.
    pause
)
