#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频录制模块
"""

import pyaudio
import wave
import threading
import time
import os
from datetime import datetime
import numpy as np

class AudioRecorder:
    def __init__(self, sample_rate=16000, channels=1, sample_width=2):
        """
        初始化音频录制器
        
        Args:
            sample_rate: 采样率 (Hz)
            channels: 声道数
            sample_width: 采样位宽 (字节)
        """
        self.sample_rate = sample_rate
        self.channels = channels
        self.sample_width = sample_width
        self.chunk_size = 1024
        
        self.is_recording = False
        self.audio_data = []
        self.audio_thread = None
        
        # 初始化PyAudio
        self.audio = pyaudio.PyAudio()
        
        # 检查音频设备
        self._check_audio_devices()
    
    def _check_audio_devices(self):
        """检查可用的音频设备"""
        print("可用的音频输入设备:")
        for i in range(self.audio.get_device_count()):
            device_info = self.audio.get_device_info_by_index(i)
            if device_info['maxInputChannels'] > 0:
                print(f"  {i}: {device_info['name']} - {device_info['maxInputChannels']}声道")
    
    def start_recording(self, output_file=None):
        """
        开始录音
        
        Args:
            output_file: 输出文件路径，如果为None则自动生成
        """
        if self.is_recording:
            raise Exception("已经在录音中")
        
        if output_file is None:
            # 自动生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"recording_{timestamp}.wav"
        
        self.output_file = output_file
        self.is_recording = True
        self.audio_data = []
        
        # 启动录音线程
        self.audio_thread = threading.Thread(target=self._record_audio)
        self.audio_thread.start()
        
        print(f"开始录音，输出文件: {output_file}")
    
    def stop_recording(self):
        """
        停止录音并保存文件
        
        Returns:
            str: 保存的文件路径
        """
        if not self.is_recording:
            raise Exception("没有在录音")
        
        self.is_recording = False
        
        # 等待录音线程结束
        if self.audio_thread:
            self.audio_thread.join()
        
        # 保存音频文件
        if self.audio_data:
            self._save_audio_file()
            print(f"录音完成，文件保存为: {self.output_file}")
            return self.output_file
        else:
            print("没有录制到音频数据")
            return None
    
    def _record_audio(self):
        """录音线程函数"""
        try:
            # 打开音频流
            stream = self.audio.open(
                format=self.audio.get_format_from_width(self.sample_width),
                channels=self.channels,
                rate=self.sample_rate,
                input=True,
                frames_per_buffer=self.chunk_size
            )
            
            print("录音中... (按停止录音按钮结束)")
            
            while self.is_recording:
                try:
                    # 读取音频数据
                    data = stream.read(self.chunk_size, exception_on_overflow=False)
                    self.audio_data.append(data)
                except Exception as e:
                    print(f"读取音频数据错误: {e}")
                    break
            
            # 关闭音频流
            stream.stop_stream()
            stream.close()
            
        except Exception as e:
            print(f"录音错误: {e}")
            self.is_recording = False
    
    def _save_audio_file(self):
        """保存音频文件"""
        try:
            with wave.open(self.output_file, 'wb') as wav_file:
                wav_file.setnchannels(self.channels)
                wav_file.setsampwidth(self.sample_width)
                wav_file.setframerate(self.sample_rate)
                
                # 写入音频数据
                for data in self.audio_data:
                    wav_file.writeframes(data)
                    
        except Exception as e:
            raise Exception(f"保存音频文件失败: {e}")
    
    def get_audio_info(self):
        """获取当前录音信息"""
        if self.audio_data:
            total_frames = len(self.audio_data) * self.chunk_size
            duration = total_frames / self.sample_rate
            return {
                'duration': duration,
                'sample_rate': self.sample_rate,
                'channels': self.channels,
                'sample_width': self.sample_width,
                'total_frames': total_frames
            }
        return None
    
    def get_audio_level(self):
        """获取当前音频电平（用于显示录音状态）"""
        if self.audio_data and len(self.audio_data) > 0:
            # 获取最近的音频数据
            recent_data = self.audio_data[-1]
            
            # 转换为numpy数组
            audio_array = np.frombuffer(recent_data, dtype=np.int16)
            
            # 计算RMS电平
            rms = np.sqrt(np.mean(audio_array**2))
            
            # 转换为分贝
            if rms > 0:
                db = 20 * np.log10(rms / 32767.0)
                return max(db, -60)  # 限制最小值为-60dB
            else:
                return -60
        return -60
    
    def test_microphone(self, duration=3):
        """
        测试麦克风
        
        Args:
            duration: 测试时长（秒）
        """
        print(f"测试麦克风 {duration} 秒...")
        
        try:
            # 开始录音
            self.start_recording("mic_test.wav")
            
            # 录音指定时长
            time.sleep(duration)
            
            # 停止录音
            output_file = self.stop_recording()
            
            if output_file and os.path.exists(output_file):
                # 分析录音质量
                info = self.get_audio_info()
                if info:
                    print(f"录音成功:")
                    print(f"  时长: {info['duration']:.2f} 秒")
                    print(f"  采样率: {info['sample_rate']} Hz")
                    print(f"  声道数: {info['channels']}")
                    print(f"  位深: {info['sample_width'] * 8} bit")
                
                # 检查音频电平
                with wave.open(output_file, 'rb') as wav_file:
                    frames = wav_file.readframes(-1)
                    audio_array = np.frombuffer(frames, dtype=np.int16)
                    
                    if len(audio_array) > 0:
                        max_amplitude = np.max(np.abs(audio_array))
                        rms = np.sqrt(np.mean(audio_array**2))
                        
                        print(f"  最大振幅: {max_amplitude} ({max_amplitude/32767*100:.1f}%)")
                        print(f"  RMS电平: {rms:.0f}")
                        
                        if max_amplitude < 1000:
                            print("  警告: 音频信号很弱，请检查麦克风音量")
                        elif max_amplitude > 30000:
                            print("  警告: 音频信号过强，可能出现削波")
                        else:
                            print("  音频信号正常")
                    else:
                        print("  错误: 没有检测到音频信号")
                
                return True
            else:
                print("录音失败")
                return False
                
        except Exception as e:
            print(f"麦克风测试失败: {e}")
            return False
    
    def __del__(self):
        """析构函数"""
        if hasattr(self, 'audio'):
            self.audio.terminate()

# 音频格式转换工具
class AudioConverter:
    @staticmethod
    def convert_to_standard_format(input_file, output_file=None):
        """
        转换音频文件为标准格式 (16kHz, 单声道, 16bit)
        
        Args:
            input_file: 输入文件路径
            output_file: 输出文件路径，如果为None则覆盖原文件
            
        Returns:
            str: 输出文件路径
        """
        if output_file is None:
            output_file = input_file
        
        try:
            # 尝试使用pydub
            from pydub import AudioSegment
            
            # 加载音频文件
            audio = AudioSegment.from_file(input_file)
            
            # 转换格式
            audio = audio.set_frame_rate(16000)  # 16kHz采样率
            audio = audio.set_channels(1)        # 单声道
            audio = audio.set_sample_width(2)    # 16bit
            
            # 导出
            audio.export(output_file, format="wav")
            
            print(f"音频格式转换完成: {output_file}")
            return output_file
            
        except ImportError:
            print("需要安装pydub库: pip install pydub")
            return None
        except Exception as e:
            print(f"音频格式转换失败: {e}")
            return None

# 示例用法
if __name__ == "__main__":
    # 创建录音器
    recorder = AudioRecorder()
    
    # 测试麦克风
    print("=== 麦克风测试 ===")
    recorder.test_microphone(3)
    
    # 交互式录音
    print("\n=== 交互式录音 ===")
    input("按回车开始录音...")
    
    recorder.start_recording("test_recording.wav")
    input("录音中... 按回车停止录音")
    
    output_file = recorder.stop_recording()
    if output_file:
        print(f"录音文件: {output_file}")
        
        # 显示音频信息
        info = recorder.get_audio_info()
        if info:
            print(f"录音时长: {info['duration']:.2f} 秒")
